# 用户管理API文档

## 概述

用户管理模块提供了用户创建、配置管理等功能。所有接口都使用POST方法，返回统一的响应格式。

## 接口列表

### 1. 创建用户（简化版）

**接口地址：** `POST /web-assistant/v1/user/createUserSimple`

**接口描述：** 只需要提供user_id即可创建用户，会自动创建默认配置

**请求参数：**
```json
{
  "userId": "user_123456"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID，长度1-100字符 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "userId": "user_123456",
    "username": null,
    "passwordHash": null,
    "email": null,
    "avatarUrl": null,
    "createTime": "2024-01-20T10:30:00.000Z",
    "updateTime": "2024-01-20T10:30:00.000Z",
    "isDeleted": false
  }
}
```

### 2. 获取用户配置信息

**接口地址：** `POST /web-assistant/v1/user/getUserConfig`

**接口描述：** 根据user_id获取用户的全局配置信息，包括翻译设置、悬浮球状态等

**请求参数：**
```json
{
  "userId": "user_123456"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "userId": "user_123456",
    "sourceLanguage": "auto",
    "targetLanguage": "zh-CN",
    "floatingBallExpanded": false,
    "blockedWebsites": [],
    "createTime": "2024-01-20T10:30:00.000Z",
    "updateTime": "2024-01-20T10:30:00.000Z",
    "isDeleted": false
  }
}
```

### 3. 更新用户配置信息

**接口地址：** `POST /web-assistant/v1/user/updateUserConfig`

**接口描述：** 更新用户的全局配置信息，支持部分字段更新

**请求参数：**
```json
{
  "userId": "user_123456",
  "sourceLanguage": "en",
  "targetLanguage": "zh-CN",
  "floatingBallExpanded": true,
  "blockedWebsites": ["https://example.com", "https://test.com"]
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID |
| sourceLanguage | string | 否 | 翻译源语言，长度1-10字符 |
| targetLanguage | string | 否 | 翻译目标语言，长度1-10字符 |
| floatingBallExpanded | boolean | 否 | 悬浮球是否展开 |
| blockedWebsites | string[] | 否 | 禁用网站URL数组 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "userId": "user_123456",
    "sourceLanguage": "en",
    "targetLanguage": "zh-CN",
    "floatingBallExpanded": true,
    "blockedWebsites": ["https://example.com", "https://test.com"],
    "createTime": "2024-01-20T10:30:00.000Z",
    "updateTime": "2024-01-20T10:35:00.000Z",
    "isDeleted": false
  }
}
```

### 4. 创建用户（完整信息）

**接口地址：** `POST /web-assistant/v1/user/createUser`

**接口描述：** 创建用户的完整版本，需要提供用户名和密码等信息

**请求参数：**
```json
{
  "username": "zhangsan",
  "passwordHash": "hashed_password",
  "email": "<EMAIL>",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都使用POST方法
2. 请求和响应的Content-Type都是application/json
3. userId是用户的唯一标识，建议使用有意义的字符串（如：user_123456）
4. 创建用户时会自动创建默认配置，无需单独调用配置创建接口
5. 更新配置时支持部分字段更新，未传递的字段保持原值不变
6. 如果用户配置不存在，更新配置接口会自动创建新的配置记录

## 数据库表结构

### 用户基本信息表 (ai_assistant_user_base_info)
- id: 主键ID
- user_id: 用户ID（唯一）
- username: 用户名（可空）
- password_hash: 密码哈希值（可空）
- email: 邮箱（可空）
- avatar_url: 头像URL（可空）

### 用户全局配置表 (ai_assistant_user_global_config)
- id: 主键ID
- user_id: 用户ID（外键）
- translate_source_language: 翻译源语言
- translate_target_language: 翻译目标语言
- floating_ball_expanded: 悬浮球是否展开
- blocked_websites: 禁用网站URL数组（JSON格式）
