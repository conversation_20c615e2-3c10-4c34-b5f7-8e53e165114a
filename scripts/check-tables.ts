import { AppDataSource } from '../src/data-source';

async function checkTables() {
  try {
    await AppDataSource.initialize();
    console.log('数据库连接成功！');
    
    // 查询所有以 ai_assistant_ 开头的表
    const query = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME LIKE 'ai_assistant_%'
      ORDER BY TABLE_NAME
    `;
    
    const tables = await AppDataSource.query(query, [AppDataSource.options.database]);
    
    console.log('\n找到的表：');
    if (tables.length === 0) {
      console.log('❌ 没有找到以 ai_assistant_ 开头的表');
    } else {
      tables.forEach((table: any, index: number) => {
        console.log(`✅ ${index + 1}. ${table.TABLE_NAME}`);
      });
    }
    
    // 检查迁移记录
    const migrations = await AppDataSource.query('SELECT * FROM migrations ORDER BY timestamp DESC LIMIT 5');
    console.log('\n最近的迁移记录：');
    migrations.forEach((migration: any) => {
      console.log(`📝 ${migration.name} (${new Date(migration.timestamp).toLocaleString()})`);
    });
    
  } catch (error) {
    console.error('❌ 错误:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

checkTables();
