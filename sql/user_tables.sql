-- 用户相关表结构
-- 基于 src/modules/user/entities/ 中的实体定义

-- 1. 用户基本信息表
DROP TABLE IF EXISTS `ai_assistant_user_base_info`;
CREATE TABLE `ai_assistant_user_base_info` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除（软删除标记）',
    `user_id` VARCHAR(100) NOT NULL COMMENT '用户ID',
    `username` VARCHAR(100) NULL COMMENT '用户名',
    `password_hash` VARCHAR(255) NULL COMMENT '密码哈希值',
    `email` VARCHAR(100) NULL COMMENT '邮箱',
    `avatar_url` VARCHAR(500) NULL COMMENT '头像URL',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基本信息表';

-- 2. 用户全局配置表
DROP TABLE IF EXISTS `ai_assistant_user_global_config`;
CREATE TABLE `ai_assistant_user_global_config` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除（软删除标记）',
    `user_id` VARCHAR(100) NOT NULL COMMENT '外键，关联用户基本信息表',
    `translate_source_language` VARCHAR(10) NOT NULL DEFAULT 'auto' COMMENT '翻译源语言',
    `translate_target_language` VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '翻译目标语言',
    `floating_ball_expanded` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '悬浮球是否展开',
    `blocked_websites` JSON NULL COMMENT '禁用网站URL数组（JSON格式存储）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_is_deleted` (`is_deleted`),
    CONSTRAINT `fk_user_global_config_user_id` FOREIGN KEY (`user_id`) REFERENCES `ai_assistant_user_base_info` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户全局配置表';

-- 3. 用户技能配置表
DROP TABLE IF EXISTS `ai_assistant_user_skill_config`;
CREATE TABLE `ai_assistant_user_skill_config` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time` DATETIME(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除（软删除标记）',
    `user_id` VARCHAR(100) NOT NULL COMMENT '外键，关联用户基本信息表',
    `skill_name` VARCHAR(100) NOT NULL COMMENT '技能名称',
    `skill_description` TEXT NULL COMMENT '技能描述',
    `skill_prompt` TEXT NOT NULL COMMENT '技能prompt模板',
    `status` INT NOT NULL DEFAULT 1 COMMENT '技能状态 (0-禁用，1-启用)',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_skill_name` (`skill_name`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_is_deleted` (`is_deleted`),
    CONSTRAINT `fk_user_skill_config_user_id` FOREIGN KEY (`user_id`) REFERENCES `ai_assistant_user_base_info` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户技能配置表';

-- 插入测试数据（可选）
-- INSERT INTO `ai_assistant_user_base_info` (`user_id`, `username`, `password_hash`, `email`, `avatar_url`) VALUES
-- ('user_123456', 'testuser', '$2b$10$example_hash', '<EMAIL>', 'https://example.com/avatar.jpg');

-- INSERT INTO `ai_assistant_user_global_config` (`user_id`, `translate_source_language`, `translate_target_language`) VALUES
-- ('user_123456', 'auto', 'zh-CN');

-- INSERT INTO `ai_assistant_user_skill_config` (`user_id`, `skill_name`, `skill_description`, `skill_prompt`, `status`) VALUES
-- ('user_123456', '翻译助手', '帮助用户进行多语言翻译', '请将以下内容翻译为{target_language}：{content}', 1);
