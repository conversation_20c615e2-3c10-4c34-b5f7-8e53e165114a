import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTablesForUserId1753348275176 implements MigrationInterface {
  name = 'UpdateUserTablesForUserId1753348275176';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. 为用户基本信息表添加user_id字段
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      ADD COLUMN \`user_id\` VARCHAR(100) NOT NULL COMMENT '用户ID' AFTER \`is_deleted\`
    `);

    // 2. 为user_id字段添加唯一索引
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      ADD UNIQUE KEY \`uk_user_id\` (\`user_id\`)
    `);

    // 3. 将username和password_hash字段改为可空
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      MODIFY COLUMN \`username\` VARCHAR(100) NULL COMMENT '用户名'
    `);

    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      MODIFY COLUMN \`password_hash\` VARCHAR(255) NULL COMMENT '密码哈希值'
    `);

    // 4. 更新用户配置表的user_id字段类型
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_global_config\` 
      MODIFY COLUMN \`user_id\` VARCHAR(100) NOT NULL COMMENT '外键，关联用户基本信息表'
    `);

    // 5. 更新用户技能表的user_id字段类型
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_skill_config\` 
      MODIFY COLUMN \`user_id\` VARCHAR(100) NOT NULL COMMENT '外键，关联用户基本信息表'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚操作：恢复原始表结构
    
    // 1. 恢复用户技能表的user_id字段类型
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_skill_config\` 
      MODIFY COLUMN \`user_id\` bigint unsigned NOT NULL COMMENT '外键，关联用户基本信息表'
    `);

    // 2. 恢复用户配置表的user_id字段类型
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_global_config\` 
      MODIFY COLUMN \`user_id\` bigint unsigned NOT NULL COMMENT '外键，关联用户基本信息表'
    `);

    // 3. 恢复username和password_hash字段为非空
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      MODIFY COLUMN \`password_hash\` VARCHAR(255) NOT NULL COMMENT '密码哈希值'
    `);

    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      MODIFY COLUMN \`username\` VARCHAR(100) NOT NULL COMMENT '用户名'
    `);

    // 4. 删除user_id字段的唯一索引
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      DROP INDEX \`uk_user_id\`
    `);

    // 5. 删除user_id字段
    await queryRunner.query(`
      ALTER TABLE \`ai_assistant_user_base_info\` 
      DROP COLUMN \`user_id\`
    `);
  }
}
