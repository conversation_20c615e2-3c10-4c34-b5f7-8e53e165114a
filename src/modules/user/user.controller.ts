import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { UserService } from './user.service';
import {
  CreateUserDto,
  CreateUserSimpleDto,
  GetUserConfigDto,
  UpdateUserConfigRequestDto,
  UpdateUserDto,
  CreateUserConfigDto,
  UpdateUserConfigDto,
  CreateUserSkillDto,
  UpdateUserSkillDto,
} from './dto';

@ApiTags('用户管理')
@Controller('/v1/user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/createUser')
  @ApiOperation({ summary: '创建用户（完整信息）' })
  async createUser(@Body() createUserDto: CreateUserDto) {
    try {
      const user = await this.userService.createUser(createUserDto);
      return new Res().success(user);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }

  @Post('/createUserSimple')
  @ApiOperation({
    summary: '创建用户（简化版）',
    description: '只需要提供user_id即可创建用户，会自动创建默认配置',
  })
  async createUserSimple(@Body() createUserSimpleDto: CreateUserSimpleDto) {
    try {
      const user = await this.userService.createUserSimple(createUserSimpleDto);
      return new Res().success(user);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }

  @Post('/getUserConfig')
  @ApiOperation({
    summary: '根据user_id获取用户配置信息',
    description: '获取用户的全局配置信息，包括翻译设置、悬浮球状态等',
  })
  async getUserConfig(@Body() getUserConfigDto: GetUserConfigDto) {
    try {
      const config = await this.userService.getUserConfig(
        getUserConfigDto.userId
      );
      if (!config) {
        return new Res().error(new ErrorCode().DEMO_UNKNOWN, '用户配置不存在');
      }
      return new Res().success(config);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }

  @Post('/updateUserConfig')
  @ApiOperation({
    summary: '更新用户配置信息',
    description: '更新用户的全局配置信息，支持部分字段更新',
  })
  async updateUserConfig(
    @Body() updateUserConfigDto: UpdateUserConfigRequestDto
  ) {
    try {
      const config = await this.userService.updateUserConfig(
        updateUserConfigDto
      );
      return new Res().success(config);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }
}
