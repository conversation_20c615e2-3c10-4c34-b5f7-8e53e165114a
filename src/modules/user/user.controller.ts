import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { UserService } from './user.service';
import {
  CreateUserDto,
  UpdateUserDto,
  CreateUserConfigDto,
  UpdateUserConfigDto,
  CreateUserSkillDto,
  UpdateUserSkillDto,
} from './dto';

@ApiTags('用户管理')
@Controller('/v1/user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/createUser')
  @ApiOperation({ summary: '创建用户' })
  async createUser(@Body() createUserDto: CreateUserDto) {
    try {
      const user = await this.userService.createUser(createUserDto);
      return new Res().success(user);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }
}
