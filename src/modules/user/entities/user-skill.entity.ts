import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Base } from '../../../common/model/base.model';

@Entity('ai_assistant_user_skill_config')
export class UserSkill extends Base {
  @Column({ name: 'user_id', comment: '外键，关联用户基本信息表' })
  userId: number;

  @Column({ name: 'skill_name', length: 100, comment: '技能名称' })
  skillName: string;

  @Column({
    name: 'skill_description',
    type: 'text',
    nullable: true,
    comment: '技能描述',
  })
  skillDescription?: string;

  @Column({ name: 'skill_prompt', type: 'text', comment: '技能prompt模板' })
  skillPrompt: string;

  @Column({
    name: 'status',
    type: 'int',
    default: 1,
    comment: '技能状态 (0-禁用，1-启用)',
  })
  status: number;

  // 关联关系
  @ManyToOne('User', 'skills')
  @JoinColumn({ name: 'user_id' })
  user: any;
}
