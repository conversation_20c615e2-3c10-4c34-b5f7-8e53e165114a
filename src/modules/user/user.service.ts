import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  paginate,
  Pagination,
  IPaginationOptions,
} from 'nestjs-typeorm-paginate';

import { User } from './entities/user.entity';
import { UserConfig } from './entities/user-config.entity';
import { UserSkill } from './entities/user-skill.entity';
import {
  CreateUserDto,
  CreateUserSimpleDto,
  UpdateUserDto,
  CreateUserConfigDto,
  UpdateUserConfigDto,
  UpdateUserConfigRequestDto,
  CreateUserSkillDto,
  UpdateUserSkillDto,
} from './dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    @InjectRepository(UserSkill)
    private readonly userSkillRepository: Repository<UserSkill>
  ) {}

  // 用户基本信息服务
  async createUser(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  // 简化的用户创建服务 - 只需要userId
  async createUserSimple(createUserSimpleDto: CreateUserSimpleDto): Promise<User> {
    // 检查用户是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { userId: createUserSimpleDto.userId }
    });

    if (existingUser) {
      return existingUser;
    }

    const user = this.userRepository.create({
      userId: createUserSimpleDto.userId,
    });

    const savedUser = await this.userRepository.save(user);

    // 创建用户的默认配置
    await this.createDefaultUserConfig(createUserSimpleDto.userId);

    return savedUser;
  }

  // 创建用户默认配置
  private async createDefaultUserConfig(userId: string): Promise<UserConfig> {
    const defaultConfig = this.userConfigRepository.create({
      userId,
      sourceLanguage: 'auto',
      targetLanguage: 'zh-CN',
      floatingBallExpanded: false,
      blockedWebsites: [],
    });

    return this.userConfigRepository.save(defaultConfig);
  }

  // 根据userId获取用户配置信息
  async getUserConfig(userId: string): Promise<UserConfig | null> {
    return this.userConfigRepository.findOne({
      where: { userId }
    });
  }

  // 更新用户配置信息
  async updateUserConfig(updateData: UpdateUserConfigRequestDto): Promise<UserConfig> {
    const { userId, ...configData } = updateData;

    // 查找现有配置
    let userConfig = await this.userConfigRepository.findOne({
      where: { userId }
    });

    if (!userConfig) {
      // 如果配置不存在，创建新的配置
      userConfig = this.userConfigRepository.create({
        userId,
        ...configData,
        // 设置默认值
        sourceLanguage: configData.sourceLanguage || 'auto',
        targetLanguage: configData.targetLanguage || 'zh-CN',
        floatingBallExpanded: configData.floatingBallExpanded || false,
        blockedWebsites: configData.blockedWebsites || [],
      });
    } else {
      // 更新现有配置
      Object.assign(userConfig, configData);
    }

    return this.userConfigRepository.save(userConfig);
  }
}
