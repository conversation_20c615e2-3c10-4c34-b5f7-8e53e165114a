import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  paginate,
  Pagination,
  IPaginationOptions,
} from 'nestjs-typeorm-paginate';

import { User } from './entities/user.entity';
import { UserConfig } from './entities/user-config.entity';
import { UserSkill } from './entities/user-skill.entity';
import {
  CreateUserDto,
  UpdateUserDto,
  CreateUserConfigDto,
  UpdateUserConfigDto,
  CreateUserSkillDto,
  UpdateUserSkillDto,
} from './dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    @InjectRepository(UserSkill)
    private readonly userSkillRepository: Repository<UserSkill>
  ) {}

  // 用户基本信息服务
  async createUser(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }
}
