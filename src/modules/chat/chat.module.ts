import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30秒超时
      maxRedirects: 5,
      // 添加更多HTTP配置以避免socket hang up
      maxContentLength: 50 * 1024 * 1024, // 50MB
      maxBodyLength: 50 * 1024 * 1024, // 50MB
      // 禁用HTTP Agent复用，避免连接复用问题
      httpAgent: false,
      httpsAgent: false,
    }),
  ],
  controllers: [ChatController],
  providers: [ChatService],
  exports: [ChatService],
})
export class ChatModule {}
