/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

/**
 * 创建会话请求 DTO
 */
export class CreateSessionDto {
  @ApiProperty({
    description: '系统id，标识平台',
    example: 'web-assiatant',
  })
  @IsString()
  appId: string;

  @ApiProperty({
    description: '当前系统登录用户id',
    example: '021961',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'hiagent接口涉及，鉴权字段',
    required: false,
    example: 'your-app-key',
  })
  @IsOptional()
  @IsString()
  appKey?: string;
}

/**
 * 创建会话响应 DTO
 */
export class CreateSessionResponseDto {
  @ApiProperty({
    description: '状态码，0表示成功，非0表示失败',
    example: '0',
  })
  code: string;

  @ApiProperty({
    description: '失败时的错误消息',
    example: '操作成功',
  })
  msg: string;

  @ApiProperty({
    description: '响应数据',
    type: 'object',
    properties: {
      conversationId: {
        type: 'string',
        description: '会话ID',
        example: 'conv_123456789',
      },
    },
  })
  resultData: {
    conversationId: string;
  };
}
