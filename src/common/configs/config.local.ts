import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import type { Config } from './config.interface';

const config: Config = {
  nest: {
    port: 3000,
  },
  cors: {
    enabled: true,
  },
  // 数据库配置
  mysql: {
    type: 'mysql',
    host: '************',
    port: 3306,
    username: 'mars',
    password: 'GCYJVN4os_V_Tict',
    database: 'byteflow',
    autoLoadEntities: true,
    // 扫描本项目中.entity.ts或者.entity.js的文件
    entities: [`${__dirname}/**/*.entity{.ts,.js}`],
    synchronize: true, // 临时开启，仅用于开发测试
    logging: true,
    namingStrategy: new SnakeNamingStrategy(),
  },
  // redis申请见http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=20775267
  redis: {
    nodes: [
      {
        host: 'x.x.x.x',
        port: 9000,
      },
      {
        host: 'x.x.x.x',
        port: 9000,
      },
    ],
    redisOptions: {
      password: '123456',
    },
  },
  swagger: {
    enabled: true,
    title: 'Nestjs FTW',
    description: 'The nestjs API description',
    version: '1.5',
    path: 'api/docs',
  },
  graphql: {
    playground: true,
    path: '/web-assistant/graphql',
    maxComplexity: 20,
  },
  apolloConfig: {
    // configServerUrl 和 cachedConfigFilePath 走默认配置
    // configServerUrl: 'http://xxx.xxx.xxx.xxx',
    // cachedConfigFilePath: '/tmp',
    appId: 'nextjs-test.c0279a025158c28d9c55d652ca68effe',
    cluster: 'default',
    namespaces: ['application'],
    initialConfigs: {},
    listenOnNotification: true,
    fetchCacheInterval: 5 * 60e3,
  },
  nodeTraceConfig: {
    appName: 'web-assistant',
    env: 'dev',
  },
};
export default config;
