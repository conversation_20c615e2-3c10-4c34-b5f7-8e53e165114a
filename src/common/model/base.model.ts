import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
} from 'typeorm';

export abstract class Base {
  @PrimaryGeneratedColumn({ comment: '主键ID' })
  id: number;

  @CreateDateColumn({
    name: 'create_time',
    type: 'datetime',
    comment: '创建时间',
  })
  createTime: Date;

  @UpdateDateColumn({
    name: 'update_time',
    type: 'datetime',
    comment: '更新时间',
  })
  updateTime: Date;

  @Column({
    name: 'is_deleted',
    type: 'boolean',
    default: false,
    comment: '是否删除（软删除标记）',
  })
  isDeleted: boolean;
}
