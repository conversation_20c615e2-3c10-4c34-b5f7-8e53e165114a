import {
  Injectable,
  CallH<PERSON><PERSON>,
  NestInterceptor,
  ExecutionContext,
} from '@nestjs/common';
import { Logger } from 'winston';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Tracer } from 'src/utils/node-tracer';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    private readonly logger: Logger,
    private readonly tracer: Tracer
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest();
    const now = Date.now();
    // This is for REST petitions
    if (request) {
      const content = `${request.method} -> ${request.url}`;
      const url = request.originalUrl;
      const body = request?.body ?? {};
      const res = JSON.stringify({ url, body });

      const traceid = request?.headers?.traceid; // 获取traceId

      this.logger.info({
        message: `收到请求：${content} Request: ${res}`,
        traceid,
      });
      const now = Date.now();

      return next.handle().pipe(
        tap((data) => {
          this.logger.info({
            message: `响应请求：${content} Response: ${Date.now() - now}ms, ${
              context.getClass().name
            }.${context.getHandler().name}, data: ${JSON.stringify(data)}`,
            traceid,
          });
          this.tracer.sendMessage(request, now, true);
        }),
        catchError((error) => {
          this.logger.error({
            message: `响应请求: ${content} ErrorResponse: ${error}`,
            traceid,
            error: error.response || error,
          });
          // 不要掩盖原始错误，直接重新抛出
          throw error;
        })
      );
      // eslint-disable-next-line no-else-return
    } else {
      // This is for GRAPHQL petitions
      const ctx: any = GqlExecutionContext.create(context);
      const resolverName = ctx.constructorRef.name;
      const info = ctx.getInfo();
      const { fieldName, parentType, variableValues } = info;
      const content = `${parentType} -> ${fieldName}`;
      const res = JSON.stringify({ variableValues });
      this.logger.info({
        message: `收到GQL请求：${content} Request: ${res}`,
      });
      return next.handle().pipe(
        tap((data) =>
          this.logger.info({
            message: `响应GQL请求：${content} Response: ${
              Date.now() - now
            }ms, data: ${JSON.stringify(data)}, ${resolverName}`,
          })
        ),
        catchError((error) => {
          const gqlErrorMsg = error?.response?.message?.join(',');
          this.logger.info({
            message: `响应GQL请求: ${content} ErrorResponse: ${
              gqlErrorMsg || error
            }`,
          });
          throw error;
        })
      );
    }
  }
}
