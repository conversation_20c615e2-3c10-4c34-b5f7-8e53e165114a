{"name": "web-assistant-bff", "version": "1.0.0", "description": "基于nestjs框架的服务端开发模板", "keywords": ["<PERSON><PERSON><PERSON>", "typescript", "passport"], "author": "", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "dev": "nest start --watch", "test": "jest", "dev:debug": "nest start --debug --watch", "start:dev": "REGION=dev node dist/main", "start:sit": "REGION=sit node dist/main", "start:prd:aoti": "REGION=prd AREA=aoti node dist/main", "start:prd:jishan": "REGION=prd AREA=jishan node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prepare": "husky install && node ./.husky/prepare.js", "module:gen": "schematics ./schematics/custom-schematics:custom-module --dry-run=false --name", "typeorm": "typeorm-ts-node-commonjs -d src/data-source.ts", "migration:generate": "npm run typeorm -- migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@apollo/server": "^4.7.3", "@graphql-tools/utils": "^10.0.1", "@ht/node-trace": "^0.1.5", "@ht/winston-transport-octopus": "0.1.3", "@liaoliaots/nestjs-redis": "9.0.5", "@nestjs/apollo": "^11.0.3", "@nestjs/axios": "^0.1.0", "@nestjs/common": "9.0.5", "@nestjs/config": "2.2.0", "@nestjs/core": "^9.4.0", "@nestjs/graphql": "^11.0.5", "@nestjs/jwt": "9.0.0", "@nestjs/passport": "9.0.0", "@nestjs/platform-express": "9.0.5", "@nestjs/swagger": "6.0.4", "@nestjs/testing": "^9.3.12", "@nestjs/typeorm": "9.0.1", "axios": "^1.3.4", "body-parser": "1.20.1", "class-transformer": "0.5.1", "class-validator": "0.13.2", "compressing": "1.6.2", "cookie": "^0.5.0", "dayjs": "1.11.6", "ejs": "3.1.8", "express": "4.18.2", "graphql": "^16.6.0", "graphql-query-complexity": "^0.12.0", "graphql-subscriptions": "^2.0.0", "ioredis": "5.3.0", "ip": "^2.0.1", "lodash": "^4.17.21", "mysql2": "2.3.3", "nest-router": "1.0.9", "nest-winston": "^1.7.0", "nestjs-ctrip-apollo-client": "^1.0.1", "nestjs-request-context": "^3.0.0", "nestjs-typeorm-paginate": "4.0.2", "node-fetch": "2", "passport": "0.6.0", "passport-jwt": "4.0.0", "pg": "^8.8.0", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.5.6", "typeorm": "0.3.10", "typeorm-naming-strategies": "^4.1.0", "uuid": "^11.0.2", "winston": "^3.8.2"}, "devDependencies": {"@angular-devkit/schematics-cli": "^17.3.6", "@babel/eslint-parser": "^7.18.9", "@babel/eslint-plugin": "^7.13.10", "@ht/eslint-config-htsc": "2.0.10-beta.1", "@nestjs/cli": "9.0.0", "@nestjs/schematics": "9.0.1", "@types/chance": "1.1.3", "@types/express": "4.17.13", "@types/jest": "28.1.6", "@types/node": "^18.0.3", "@types/supertest": "2.0.12", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "chance": "1.1.8", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-eslint-comments": "^3.1.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-markdown": "^2.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^5.1.0", "husky": "7.0.4", "jest": "28.1.3", "prettier": "^2.7.1", "stylelint": "^14.15.0", "supertest": "6.2.4", "ts-jest": "28.0.7", "ts-loader": "9.3.1", "ts-morph": "^15.1.0", "ts-node": "10.9.1", "tsconfig-paths": "4.0.0", "typescript": "*"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}, "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "license": "MIT", "templateConfig": {"name": "web-assistant", "version": "1.1.0"}}